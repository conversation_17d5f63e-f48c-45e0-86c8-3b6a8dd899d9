2025-08-13 10:26:39.166 | ERROR    | f1eb1f7d029e44af9e2f3760ca6316a7 | 获取知识库列表失败: RAGFlow服务请求超时
2025-08-13 10:27:28.976 | ERROR    | d97da7bdf5d94b7496f7038cce8cb573 | 获取知识库列表失败: RAGFlow服务请求超时
2025-08-13 10:28:39.164 | ERROR    | 8c2b6b44c93d4ab4a0ae592750a42d47 | 获取知识库列表失败: RAGFlow服务连接失败: 
2025-08-13 10:29:14.061 | ERROR    | ec46393570c94e169df2a9e963e91b60 | 获取知识库列表失败: RAGFlow服务请求超时
2025-08-13 10:31:57.530 | ERROR    | 88c1ee022c4c495bbcb6e3606bfd16a0 | 获取知识库列表失败: RAGFlow服务请求超时
2025-08-13 10:37:32.077 | ERROR    | ed277572fffc431289330de7b9ae77f5 | 更新知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <text-embedding-bge-m3@LM-Studio>
2025-08-13 10:49:35.883 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000002D58ECBA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x000002D591228B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000002D59122BA60>
    └ <uvicorn.server.Server object at 0x000002D59162F7D0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002D59122BB00>
           │       │   └ <uvicorn.server.Server object at 0x000002D59162F7D0>
           │       └ <function run at 0x000002D590A6F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002D597F71EE0>
           │      └ <function Runner.run at 0x000002D590F0B2E0>
           └ <asyncio.runners.Runner object at 0x000002D597ECD040>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002D590F08EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002D597ECD040>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002D590FD8D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002D590F0AC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002D590A64860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1812, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 55620)>
    └ <_ProactorSocketTransport closing fd=1812>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
