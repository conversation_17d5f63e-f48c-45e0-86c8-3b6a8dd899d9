#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库管理 API

提供知识库的CRUD操作接口，基于Java token认证系统进行权限控制
"""
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status

from backend.common.log import log as logger
from backend.app.iot.schema.knowledge_base import (
    KnowledgeBaseCreate,
    KnowledgeBaseQuery,
    KnowledgeBaseUpdate,
    KnowledgeBaseDelete
)
from backend.app.iot.service.knowledge_base_service import knowledge_base_service
from backend.common.response.response_schema import ResponseModel, response_base
from backend.common.response.response_code import CustomResponse
from backend.common.security.jwt import DependsJwtAuth
from backend.common.security.java_permission import require_java_permission

router = APIRouter()


@router.get(
    '/health',
    summary='知识库服务健康检查',
    response_model=ResponseModel,
    name='iot_kb_health_check'
)
async def iot_kb_health_check() -> ResponseModel:
    """
    检查知识库服务（RAGFlow）的连接状态

    不需要认证，用于系统监控
    """
    try:
        health_status = await knowledge_base_service.health_check()

        if health_status["status"] == "healthy":
            return response_base.success(
                res=CustomResponse(200, "知识库服务正常"),
                data=health_status
            )
        else:
            return response_base.fail(
                res=CustomResponse(500, "知识库服务异常"),
                data=health_status
            )

    except Exception as e:
        logger.error(f"知识库健康检查失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"健康检查失败: {str(e)}")
        )


@router.get(
    '/list',
    summary='获取知识库列表',
    response_model=ResponseModel
)
@require_java_permission("knowledge:base:list")  # ✅ 保持权限控制
async def list_knowledge_bases(
    request: Request,  # ✅ 权限装饰器需要Request对象
    query: KnowledgeBaseQuery = Depends(),  # ✅ 简化参数处理
    token: str = DependsJwtAuth  # ✅ 保持JWT认证
) -> ResponseModel:
    """
    获取知识库列表 - 权限已验证，直接代理RAGFlow

    需要knowledge:base:list权限
    """
    try:
        result = await knowledge_base_service.list_knowledge_bases(query)

        return response_base.success(
            res=CustomResponse(200, "获取知识库列表成功"),
            data=result.get("data", [])  # ✅ 直接返回RAGFlow数据
        )

    except HTTPException as e:
        logger.error(f"获取知识库列表失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"获取知识库列表失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取知识库列表失败: {str(e)}")
        )


@router.post(
    '/create',
    summary='创建知识库',
    response_model=ResponseModel
)
@require_java_permission("knowledge:base:create")  # ✅ 保持权限控制
async def create_knowledge_base(
    request: Request,  # ✅ 权限装饰器需要Request对象
    kb_data: KnowledgeBaseCreate,
    token: str = DependsJwtAuth  # ✅ 保持JWT认证
) -> ResponseModel:
    """
    创建新的知识库 - 权限已验证，直接代理RAGFlow

    需要knowledge:base:create权限
    """
    try:
        result = await knowledge_base_service.create_knowledge_base(kb_data)

        return response_base.success(
            res=CustomResponse(200, "创建知识库成功"),
            data=result.get("data")  # ✅ 直接返回RAGFlow数据
        )

    except HTTPException as e:
        logger.error(f"创建知识库失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"创建知识库失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"创建知识库失败: {str(e)}")
        )


@router.get(
    '/{kb_id}',
    summary='获取知识库详情',
    response_model=ResponseModel
)
@require_java_permission("knowledge:base:view")  # ✅ 保持权限控制
async def get_knowledge_base_detail(
    request: Request,  # ✅ 权限装饰器需要Request对象
    kb_id: str,
    token: str = DependsJwtAuth  # ✅ 保持JWT认证
) -> ResponseModel:
    """
    获取指定知识库的详细信息 - 权限已验证，直接代理RAGFlow

    需要knowledge:base:view权限
    """
    try:
        result = await knowledge_base_service.get_knowledge_base(kb_id)

        return response_base.success(
            res=CustomResponse(200, "获取知识库详情成功"),
            data=result.get("data")  # ✅ 直接返回RAGFlow数据
        )

    except HTTPException as e:
        logger.error(f"获取知识库详情失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"获取知识库详情失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取知识库详情失败: {str(e)}")
        )


@router.put(
    '/{kb_id}',
    summary='更新知识库',
    response_model=ResponseModel
)
@require_java_permission("knowledge:base:update")  # ✅ 保持权限控制
async def update_knowledge_base(
    request: Request,  # ✅ 权限装饰器需要Request对象
    kb_id: str,
    kb_data: KnowledgeBaseUpdate,
    token: str = DependsJwtAuth  # ✅ 保持JWT认证
) -> ResponseModel:
    """
    更新指定知识库的信息 - 权限已验证，直接代理RAGFlow

    需要knowledge:base:update权限
    """
    try:
        result = await knowledge_base_service.update_knowledge_base(kb_id, kb_data)

        return response_base.success(
            res=CustomResponse(200, "更新知识库成功"),
            data=result.get("data")  # ✅ 直接返回RAGFlow数据
        )

    except HTTPException as e:
        logger.error(f"更新知识库失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"更新知识库失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"更新知识库失败: {str(e)}")
        )


@router.delete(
    '/delete',
    summary='删除知识库',
    response_model=ResponseModel
)
@require_java_permission("knowledge:base:delete")  # ✅ 保持权限控制
async def delete_knowledge_bases(
    request: Request,  # ✅ 权限装饰器需要Request对象
    delete_data: KnowledgeBaseDelete,
    token: str = DependsJwtAuth  # ✅ 保持JWT认证
) -> ResponseModel:
    """
    删除指定的知识库 - 权限已验证，直接代理RAGFlow

    需要knowledge:base:delete权限
    """
    try:
        result = await knowledge_base_service.delete_knowledge_bases(delete_data.ids)

        return response_base.success(
            res=CustomResponse(200, "删除知识库成功"),
            data=result.get("data")  # ✅ 直接返回RAGFlow数据
        )

    except HTTPException as e:
        logger.error(f"删除知识库失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"删除知识库失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"删除知识库失败: {str(e)}")
        )


@router.get(
    '/models/embedding',
    summary='获取可用的嵌入模型列表',
    response_model=ResponseModel
)
@require_java_permission("knowledge:base:view")  # ✅ 保持权限控制
async def get_available_embedding_models(
    request: Request,  # ✅ 权限装饰器需要Request对象
    token: str = DependsJwtAuth  # ✅ 保持JWT认证
) -> ResponseModel:
    """
    获取当前系统中可用的嵌入模型列表 - 权限已验证，直接代理RAGFlow

    需要knowledge:base:view权限
    """
    try:
        result = await knowledge_base_service.get_available_embedding_models()

        return response_base.success(
            res=CustomResponse(200, "获取嵌入模型列表成功"),
            data=result.get("data", [])  # ✅ 直接返回RAGFlow数据
        )

    except HTTPException as e:
        logger.error(f"获取嵌入模型列表失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"获取嵌入模型列表失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取嵌入模型列表失败: {str(e)}")
        )


@router.get(
    '/stats/overview',
    summary='获取知识库统计信息',
    response_model=ResponseModel
)
@require_java_permission("knowledge:base:stats")  # ✅ 保持权限控制
async def get_knowledge_base_stats(
    request: Request,  # ✅ 权限装饰器需要Request对象
    token: str = DependsJwtAuth  # ✅ 保持JWT认证
) -> ResponseModel:
    """
    获取知识库的统计信息 - 权限已验证，直接代理RAGFlow

    需要knowledge:base:stats权限
    """
    try:
        stats = await knowledge_base_service.get_knowledge_base_stats()

        return response_base.success(
            res=CustomResponse(200, "获取统计信息成功"),
            data=stats  # ✅ 直接返回统计数据
        )

    except HTTPException as e:
        logger.error(f"获取统计信息失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取统计信息失败: {str(e)}")
        )
